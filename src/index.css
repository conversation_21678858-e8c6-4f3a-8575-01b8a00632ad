


@import "tailwindcss";



body {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

@font-face {
  font-family: 'BlenderPro Book';
  src: url('/fonts/BlenderPro-Book.woff2') format('woff2');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
    font-family: 'Cyberpunk';
    src: url('/fonts/Cyberpunk.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}



@layer theme {
  @keyframes glitch {
    /* All your animation steps go here */
    0% { clip-path: inset(20% 0 50% 0); }
    5% { clip-path: inset(10% 0 60% 0); }
    10% { clip-path: inset(15% 0 55% 0); }
    15% { clip-path: inset(25% 0 35% 0); }
    20% { clip-path: inset(30% 0 40% 0); }
    25% { clip-path: inset(40% 0 20% 0); }
    30% { clip-path: inset(10% 0 60% 0); }
    35% { clip-path: inset(15% 0 55% 0); }
    40% { clip-path: inset(25% 0 35% 0); }
    45% { clip-path: inset(30% 0 40% 0); }
    50% { clip-path: inset(20% 0 50% 0); }
    55% { clip-path: inset(10% 0 60% 0); }
    60% { clip-path: inset(15% 0 55% 0); }
    65% { clip-path: inset(25% 0 35% 0); }
    70% { clip-path: inset(30% 0 40% 0); }
    75% { clip-path: inset(40% 0 20% 0); }
    80% { clip-path: inset(20% 0 50% 0); }
    85% { clip-path: inset(10% 0 60% 0); }
    90% { clip-path: inset(15% 0 55% 0); }
    95% { clip-path: inset(25% 0 35% 0); }
    100% { clip-path: inset(30% 0 40% 0); }
  }
}


html, body {
  font-family: 'BlenderPro Book', Arial, Helvetica, sans-serif;
}

/*
  To achieve a border that follows the clip-path shape, use a nested shell pattern:
  - The outer div (nav-border) acts as the border and uses the border color as its background, with padding for thickness.
  - The inner div (nav-content) holds the content and uses the same clip-path, filling the padded area.
  - This avoids the limitations of CSS border with clip-path.
  - See README or the HTML example for usage.
*/

.nav-border {
  background-color: cyan; /* Border color */
  padding: 20px; /* Border thickness */
  clip-path: polygon(16px 0, 100% 0, 100% 30px, calc(100% - 16px) 48px, 0 48px, 0 16px);
  width: 100%;
  max-width: 1920px;
}

.nav-content {
  background-color: rgb(255, 255, 255); /* Main background */
  clip-path: polygon(16px 0, 100% 0, 100% 30px, calc(100% - 16px) 48px, 0 48px, 0 16px);
  height: 44px;
  display: flex;
  align-items: center;
  padding-left: 24px;
}

.nav-list {
  align-items: center;
  background: transparent;
  display: flex;
  height: 44px;
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-container {
    position: relative;
    width: 100%;
    width:  397px;
    height: 44px;
}

.nav-border-layer {
    position: absolute;
    top: 0;
    left: 0;
    width: 100% !important;
    height: 100%;
    background-color: transparent; 
    clip-path: polygon(16px 0, 100% 0, 100% 30px, calc(100% - 16px) 48px, 0 48px, 0 16px);
}

.nav-content-layer {
    position: absolute;
    top: 1px;
    left: 1px;
    width: calc(100% - 3px) !important;
    height: calc(100% - 2px);
    background: rgba(10, 10, 10, 0.7);
    backdrop-filter: blur(8px);
    clip-path: polygon(16px 0, 100% 0, 100% 30px, calc(100% - 25px) 48px, 0 48px, 0 16px);
    display: flex;
    align-items: center;
    padding-left: 24px;
}

.nav-tab {
    background: none;
    border: none;
    padding: 0 1rem;
    height: 100%;
    color: white;
    font-weight: bold;
    text-transform: uppercase;
    font-size: 1rem;
    letter-spacing: 0.05em;
    cursor: pointer;
    transition: color 0.2s ease-in-out;
    display: flex;
    align-items: center;
}

.nav-tab:hover {
    color: #ffffff; 
    background: rgba(255,255,255,0.08);
    transition: color 0.2s, background 0.2s, transform 0.15s;
}

.nav-tab.active {
    background-color: cyan; 
    color: black; 
}

canvas{
    min-width: 100vw !important;
    min-height: 100vh !important;
}

/* Responsive styles for navbar */
@media (max-width: 900px) {
  .nav-container {
    width: 400px;
    max-width: 100vw;
    min-width: 0;
  }
}

@media (max-width: 600px) {
  .nav-container {
    width: 80vw;
    min-width: 0;
    height: 44px;
  }
  .nav-tab{
    font-size: 0.6rem;
  }
  
}

@media (max-width: 400px) {
  .nav-container {
    width: 60vw;
    min-width: 0;
    height: 40px;
  }
  .nav-tab{
    font-size: 0.7rem;
  }
}

.logo-main{
    font-family: 'BlenderPro book', Arial, Helvetica, sans-serif;
    font-size: 4rem;
    color: cyan
}




.text-glow {
  text-shadow: 0 0 4px #22d3ee50, 0 0 6px #22d3ee50;
}
.box-glow {
    box-shadow: 0 0 4px #22d3ee50, 0 0 6px #22d3ee;
}

/* --- Glitch Effect CSS --- */
.glitch {
  position: relative;
  font-size: 2.25rem; /* text-4xl */
  font-weight: 700;
  letter-spacing: 0.3em;
  color: #FFFFFFCC; /* Base text color (white with 80% opacity) */
}

.glitch::before,
.glitch::after {
  content: attr(data-text); /* Copies the text from the data-text attribute */
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: black; /* Hides the original text behind the overlays */
  overflow: hidden;
}

.glitch::before {
  left: 2px;
  text-shadow: -2px 0 #e11d48; /* Reddish glitch color */
  animation: glitch-anim-1 7.5s infinite linear alternate-reverse;
}

.glitch::after {
  left: -2px;
  text-shadow: -2px 0 #22d3ee; /* Cyan glitch color */
  animation: glitch-anim-2 5.5s infinite linear alternate-reverse;
}

@keyframes glitch-anim-1 {
  0% { clip-path: inset(15% 0 86% 0); }
  20% { clip-path: inset(80% 0 1% 0); }
  40% { clip-path: inset(55% 0 20% 0); }
  60% { clip-path: inset(20% 0 70% 0); }
  80% { clip-path: inset(92% 0 5% 0); }
  100% { clip-path: inset(40% 0 45% 0); }
}

@keyframes glitch-anim-2 {
  0% { clip-path: inset(70% 0 26% 0); }
  20% { clip-path: inset(10% 0 81% 0); }
  40% { clip-path: inset(65% 0 10% 0); }
  60% { clip-path: inset(30% 0 60% 0); }
  80% { clip-path: inset(82% 0 8% 0); }
  100% { clip-path: inset(50% 0 35% 0); }
}



/* In src/index.css or your main stylesheet */

@layer utilities {
  /* --- KEYFRAMES DEFINITIONS --- */
  @keyframes paths {
    0% { clip-path: polygon(0% 43%, 83% 43%, 83% 22%, 23% 22%, 23% 24%, 91% 24%, 91% 26%, 18% 26%, 18% 83%, 29% 83%, 29% 17%, 41% 17%, 41% 39%, 18% 39%, 18% 82%, 54% 82%, 54% 88%, 19% 88%, 19% 4%, 39% 4%, 39% 14%, 76% 14%, 76% 52%, 23% 52%, 23% 35%, 19% 35%, 19% 8%, 36% 8%, 36% 31%, 73% 31%, 73% 16%, 1% 16%, 1% 56%, 50% 56%, 50% 8%); }
    5% { clip-path: polygon(0% 29%, 44% 29%, 44% 83%, 94% 83%, 94% 56%, 11% 56%, 11% 64%, 94% 64%, 94% 70%, 88% 70%, 88% 32%, 18% 32%, 18% 96%, 10% 96%, 10% 62%, 9% 62%, 9% 84%, 68% 84%, 68% 50%, 52% 50%, 52% 55%, 35% 55%, 35% 87%, 25% 87%, 25% 39%, 15% 39%, 15% 88%, 52% 88%); }
    30% { clip-path: polygon(0% 53%, 93% 53%, 93% 62%, 68% 62%, 68% 37%, 97% 37%, 97% 89%, 13% 89%, 13% 45%, 51% 45%, 51% 88%, 17% 88%, 17% 54%, 81% 54%, 81% 75%, 79% 75%, 79% 76%, 38% 76%, 38% 28%, 61% 28%, 61% 12%, 55% 12%, 55% 62%, 68% 62%, 68% 51%, 0% 51%, 0% 92%, 63% 92%, 63% 4%, 65% 4%); }
    45% { clip-path: polygon(0% 33%, 2% 33%, 2% 69%, 58% 69%, 58% 94%, 55% 94%, 55% 25%, 33% 25%, 33% 85%, 16% 85%, 16% 19%, 5% 19%, 5% 20%, 79% 20%, 79% 96%, 93% 96%, 93% 50%, 5% 50%, 5% 74%, 55% 74%, 55% 57%, 96% 57%, 96% 59%, 87% 59%, 87% 65%, 82% 65%, 82% 39%, 63% 39%, 63% 92%, 4% 92%, 4% 36%, 24% 36%, 24% 70%, 1% 70%, 1% 43%, 15% 43%, 15% 28%, 23% 28%, 23% 71%, 90% 71%, 90% 86%, 97% 86%, 97% 1%, 60% 1%, 60% 67%, 71% 67%, 71% 91%, 17% 91%, 17% 14%, 39% 14%, 39% 30%, 58% 30%, 58% 11%, 52% 11%, 52% 83%, 68% 83%); }
    76% { clip-path: polygon(0% 26%, 15% 26%, 15% 73%, 72% 73%, 72% 70%, 77% 70%, 77% 75%, 8% 75%, 8% 42%, 4% 42%, 4% 61%, 17% 61%, 17% 12%, 26% 12%, 26% 63%, 73% 63%, 73% 43%, 90% 43%, 90% 67%, 50% 67%, 50% 41%, 42% 41%, 42% 46%, 50% 46%, 50% 84%, 96% 84%, 96% 78%, 49% 78%, 49% 25%, 63% 25%, 63% 14%); }
    90% { clip-path: polygon(0% 41%, 13% 41%, 13% 6%, 87% 6%, 87% 93%, 10% 93%, 10% 13%, 89% 13%, 89% 6%, 3% 6%, 3% 8%, 16% 8%, 16% 79%, 0% 79%, 0% 99%, 92% 99%, 92% 90%, 5% 90%, 5% 60%, 0% 60%, 0% 48%, 89% 48%, 89% 13%, 80% 13%, 80% 43%, 95% 43%, 95% 19%, 80% 19%, 80% 85%, 38% 85%, 38% 62%); }
    1%, 7%, 33%, 47%, 78%, 93% { clip-path: none; }
  }
  @keyframes movement {
    0% { transform: translate(-20px, 0px); }
    15% { transform: translate(10px, 10px); }
    60% { transform: translate(-10px, 5px); }
    75% { transform: translate(20px, -5px); }
    100% { transform: translate(5px, 10px); }
  }
  @keyframes opacity {
    0% { opacity: 0.1; }
    5% { opacity: 0.7; }
    30% { opacity: 0.4; }
    45% { opacity: 0.6; }
    76% { opacity: 0.4; }
    90% { opacity: 0.8; }
    1%, 7%, 33%, 47%, 78%, 93% { opacity: 0; }
  }
  @keyframes font {
    0% { font-weight: 100; color: #e0287d; filter: blur(3px); }
    20% { font-weight: 500; color: #fff; filter: blur(0); }
    50% { font-weight: 300; color: #1bc7fb; filter: blur(2px); }
    60% { font-weight: 700; color: #fff; filter: blur(0); }
    90% { font-weight: 500; color: #e0287d; filter: blur(6px); }
  }


  .animate-glitch-paths { animation: paths 5s step-end infinite; }
  .animate-glitch-opacity { animation: opacity 5s step-end infinite; }
  .animate-glitch-movement { animation: movement 8s step-end infinite; }
  .animate-glitch-movement-alt { animation: movement 10s step-end infinite; }
  .animate-glitch-font { animation: font 7s step-end infinite; }
  .animate-glitch-font-alt { animation: font 8s step-end infinite; }
}




.glitch {
  position: relative;
  font-size: 1.5rem; 
  font-weight: 700;
  line-height: 1.2;
  color: #fff;
  letter-spacing: 5px;
  z-index: 1;
  text-align: center;
}

.glitch:before,
.glitch:after {
  display: block;
  content: attr(data-glitch);
  position: absolute;
  top: 0;
  left: 0;
  opacity: 0.8;
  border-radius: 0.5rem; 
}


.glitch:before {
  animation: glitch 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) both infinite;
  color: #8b00ff; 
  z-index: -1;
}


.glitch:after {
  animation: glitch 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) reverse both infinite;
  color: #00e571; 
  z-index: -2;
}


@keyframes glitch {
  0% { transform: translate(0); }
  20% { transform: translate(-3px, 3px); }
  40% { transform: translate(-3px, -3px); }
  60% { transform: translate(3px, 3px); }
  80% { transform: translate(3px, -3px); }
  to { transform: translate(0); }
}



/* For Chrome, Safari, Opera, and other WebKit-based browsers */
*::-webkit-scrollbar {
  display: none;
}

/* For Firefox, IE, and Edge */
* {
  scrollbar-width: none;
  -ms-overflow-style: none; 
}
