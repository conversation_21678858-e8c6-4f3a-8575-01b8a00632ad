import React from 'react';
import { useStore } from './state/UseStore';
import FuturisticLoader from './components/ui/Loading';
import City from './components/City';
import Overlay from './components/Overlay';
import RotateDevice from './components/ui/RotateDevice';
import { Leva } from 'leva';


function App() {
  const isLoaded = useStore(state => state.isLoaded);
  
  return (
    <main style={{ position: 'relative', width: '100vw', height: '100vh' }}>
      <Leva hidden={true} />
      <RotateDevice />
      <div style={{ position: 'absolute', inset: 0 }} className='top-0 left-0 overflow-hidden'>
        <City />
        <Overlay />
      </div>
      {!isLoaded && (
        <div style={{ position: 'absolute', inset: 0, zIndex: 100000000 }}>
          <FuturisticLoader />
        </div>
      )}
    </main>
  );
}

export default App;
