import React from 'react'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, Vignette, Noise, ChromaticAberration, DepthOfField } from '@react-three/postprocessing'
import { useControls } from 'leva'
import { BlendFunction } from 'postprocessing'

export default function Effects() {
  const bloom = useControls('Bloom', {
    enabled: { value: true },
    intensity: { value: 0.2, min: 0, max: 5, step: 0.01 },
    luminanceThreshold: { value: 0.18, min: 0, max: 1, step: 0.01 },
    luminanceSmoothing: { value: 0.025, min: 0, max: 1, step: 0.001 },
    mipmapBlur: { value: true },
    blendFunction: {
      value: BlendFunction.SCREEN,
      options: Object.entries(BlendFunction).reduce((acc, [k, v]) => {
        if (typeof v === 'number') acc[k] = v
        return acc
      }, {})
    }
  })

  const vignette = useControls('Vignette', {
    enabled: { value: true },
    eskil: { value: false },
    offset: { value: 0.19, min: 0, max: 1, step: 0.01 },
    darkness: { value: 0.70, min: 0, max: 2, step: 0.01 },
    blendFunction: {
      value: BlendFunction.NORMAL,
      options: Object.entries(BlendFunction).reduce((acc, [k, v]) => {
        if (typeof v === 'number') acc[k] = v
        return acc
      }, {})
    }
  })

  const noise = useControls('Noise', {
    enabled: { value: false },
    opacity: { value: 0.05, min: 0, max: 1, step: 0.01 },
    blendFunction: {
      value: BlendFunction.SCREEN,
      options: Object.entries(BlendFunction).reduce((acc, [k, v]) => {
        if (typeof v === 'number') acc[k] = v
        return acc
      }, {})
    }
  })

  const chromatic = useControls('Chromatic Aberration', {
    enabled: { value: true },
    offsetX: { value: 0, min: 0, max: 0.01, step: 0.0001 },
    offsetY: { value: 0, min: 0, max: 0.01, step: 0.0001 },
    blendFunction: {
      value: BlendFunction.NORMAL,
      options: Object.entries(BlendFunction).reduce((acc, [k, v]) => {
        if (typeof v === 'number') acc[k] = v
        return acc
      }, {})
    }
  })

  const dof = useControls('Depth Of Field', {
    enabled: { value: false },
    focusDistance: { value: 0.02, min: 0, max: 1, step: 0.001 },
    focalLength: { value: 0.02, min: 0, max: 1, step: 0.001 },
    bokehScale: { value: 2, min: 0, max: 10, step: 0.1 },
    height: { value: 480, min: 0, max: 1080, step: 1 }
  })

  return (
    <EffectComposer>
      {bloom.enabled && (
        <Bloom
          intensity={bloom.intensity}
          luminanceThreshold={bloom.luminanceThreshold}
          luminanceSmoothing={bloom.luminanceSmoothing}
          mipmapBlur={bloom.mipmapBlur}
          blendFunction={bloom.blendFunction}
        />
      )}
      {vignette.enabled && (
        <Vignette
          eskil={vignette.eskil}
          offset={vignette.offset}
          darkness={vignette.darkness}
          blendFunction={vignette.blendFunction}
        />
      )}
      {noise.enabled && (
        <Noise
          opacity={noise.opacity}
          blendFunction={noise.blendFunction}
        />
      )}
      {chromatic.enabled && (
        <ChromaticAberration
          offset={[chromatic.offsetX, chromatic.offsetY]}
          blendFunction={chromatic.blendFunction}
        />
      )}
      {dof.enabled && (
        <DepthOfField
          focusDistance={dof.focusDistance}
          focalLength={dof.focalLength}
          bokehScale={dof.bokehScale}
          height={dof.height}
        />
      )}
    </EffectComposer>
  )
} 