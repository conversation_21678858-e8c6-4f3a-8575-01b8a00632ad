import { useState, useEffect, useRef, useLayoutEffect } from 'react';
import { useStore } from '../../state/UseStore';
import panelSectionsData from '../../data/SectionInfo';


const SciFiSection = ({ title, models, onModelClick, notchSize = 20 }) => {
    const [dims, setDims] = useState({ width: 0, height: 0 });
    const containerRef = useRef(null);

    useLayoutEffect(() => {
        const measure = () => {
            if (containerRef.current) {
                setDims({
                    width: containerRef.current.offsetWidth,
                    height: containerRef.current.offsetHeight,
                });
            }
        };
        measure();
        const resizeObserver = new ResizeObserver(measure);
        if (containerRef.current) {
            resizeObserver.observe(containerRef.current);
        }
        return () => resizeObserver.disconnect();
    }, [models]);

    const { width, height } = dims;

    const pathData = width > 0 && height > 0
        ? `M ${notchSize} 0 H ${width} V ${height - notchSize} L ${width - notchSize} ${height} H 0 V ${notchSize} Z`
        : '';

    const fillColor = "rgba(56, 189, 248, 0.1)";
    const strokeColor = "#a5f3fc";

    return (
        <div ref={containerRef} className="relative text-cyan-50 w-full flex flex-col max-h-[85vh]">
            <svg className="absolute z-[-1] top-0 left-0 w-full h-full" preserveAspectRatio="none" aria-hidden="true">
                <path d={pathData} fill={fillColor} stroke={strokeColor} strokeWidth="2" />
            </svg>
            <div className="relative p-4 flex flex-col flex-grow min-h-0">
                <div className="flex-shrink-0">
                    <h3 className="text-xl font-bold text-white">{title}</h3>
                </div>
                <div className="border-t border-cyan-400/20 mt-4 pt-4 flex-grow overflow-y-auto pr-2">
                    <ul className="space-y-2">
                        {models.map((model) => (
                            <li
                                key={model.srNo}
                                onClick={() => onModelClick(model)}
                                className="text-slate-300 hover:text-white hover:bg-sky-400/20 p-3 rounded-md cursor-pointer transition-all duration-200"
                            >
                                <div className="font-semibold">{model.model}</div>
                                <div className="text-md text-cyan-200/90">{model.description}</div>
                            </li>
                        ))}
                    </ul>
                </div>
            </div>
        </div>
    );
};


/**
 * SidePanel Component
 * This component now manages the transition between the panel and a full-screen video player.
 */
const SidePanel = () => {
    // --- STATE MANAGEMENT ---
    const selectedSectionName = useStore(state => state.selectedSectionName);
    const selectedSubSectionName = useStore(state => state.selectedSubSectionName);
    const setSelectedSubSectionName = useStore(state => state.setSelectedSubSectionName);
    const isCameraTransitioning = useStore(state => state.isCameraTransitioning);

    // --- STATE FOR ANIMATION ORCHESTRATION ---
    const [isPlayerMounted, setIsPlayerMounted] = useState(false);
    const [isPlayerVisible, setIsPlayerVisible] = useState(false);
    // This new state now exclusively controls the panel's position.
    const [isPanelVisible, setIsPanelVisible] = useState(false);

    const isOpen = selectedSectionName !== null;

    // --- DERIVED STATE ---
    const section = isOpen ? panelSectionsData.find(sec => sec.id === selectedSectionName) : null;
    const activeModel = section?.models.find(
        model => model.srNo === selectedSubSectionName && model.availableVideos
    );
    const videoSrc = activeModel?.availableVideos;

    // --- EFFECT TO CONTROL PANEL VISIBILITY BASED ON CAMERA TRANSITION ---
    useEffect(() => {
        console.log('🎛️ Panel visibility effect:', { isOpen, isCameraTransitioning, videoSrc: !!videoSrc });

        // Don't show panel if video is playing
        if (videoSrc) {
            console.log('📹 Hiding panel - video playing');
            setIsPanelVisible(false);
            return;
        }

        if (isOpen && !isCameraTransitioning) {
            // Only show panel when section is selected AND camera transition is complete AND no video
            console.log('✅ Showing panel - section selected and camera stable');
            setIsPanelVisible(true);
        } else {
            // Hide panel when no section is selected OR camera is transitioning
            console.log('❌ Hiding panel - no section or camera moving');
            setIsPanelVisible(false);
        }
    }, [isOpen, isCameraTransitioning, videoSrc]);

    // --- EFFECT TO ORCHESTRATE VIDEO ANIMATIONS ---
    useEffect(() => {
        if (videoSrc) {
            // Sequence for SHOWING video:
            // 1. Mount player (invisibly).
            setIsPlayerMounted(true);
            // 2. After a brief delay, fade video in.
            const fadeInTimer = setTimeout(() => {
                setIsPlayerVisible(true);
            }, 100);

            return () => clearTimeout(fadeInTimer);
        } else {
            // Sequence for HIDING video:
            // This guard prevents this from running on initial load.
            if (!isPlayerMounted) return;

            // 1. Fade video out.
            setIsPlayerVisible(false);
            // 2. After video is faded out, unmount the player.
            const transitionTimer = setTimeout(() => {
                setIsPlayerMounted(false);
            }, 500); // Match player's fade duration.

            return () => clearTimeout(transitionTimer);
        }
    }, [videoSrc]);


    // --- HANDLERS ---
    const handleModelClick = (model) => {
        setSelectedSubSectionName(model.srNo);
    };

    const handleVideoEnd = () => {
        setSelectedSubSectionName(null);
    };

    if (!isOpen) return null;

    return (
        <>
            {/* DEBUG OVERLAY */}
            <div className="fixed top-4 right-4 z-[60] bg-black/80 text-white p-2 text-xs rounded">
                <div>Section: {selectedSectionName}</div>
                <div>Camera Transitioning: {isCameraTransitioning ? 'YES' : 'NO'}</div>
                <div>Panel Visible: {isPanelVisible ? 'YES' : 'NO'}</div>
                <div>Video: {videoSrc ? 'YES' : 'NO'}</div>
            </div>

            {/* --- VIDEO PLAYER --- */}
            {isPlayerMounted && (
                <div className={`fixed inset-0 z-50 bg-black flex items-center justify-center
                    ${isPlayerVisible ? 'opacity-100' : 'opacity-0'}`}
                >
                    {isPlayerVisible && (
                        <video
                            key={videoSrc}
                            src={videoSrc}
                            autoPlay
                            muted
                            onEnded={handleVideoEnd}
                            className="w-full h-auto max-h-full"
                        >
                            Your browser does not support the video tag.
                        </video>
                    )}
                </div>
            )}

            {/* --- SIDE PANEL --- */}
            {/* Now uses `isPanelVisible` to control its position */}
            <aside 
                className={`fixed top-0 right-0 h-screen w-full max-w-md p-4 z-50 flex items-center justify-center transition-transform duration-500 ease-in-out
                ${isPanelVisible ? 'translate-x-0' : 'translate-x-full'}`}
            >
                <div className="relative w-full">
                    {section ? (
                        <SciFiSection
                            title={section.title}
                            models={section.models}
                            onModelClick={handleModelClick}
                        />
                    ) : (
                        <div className="text-white bg-red-500/20 p-4 rounded-lg">
                            Error: Section not found.
                        </div>
                    )}
                </div>
            </aside>
        </>
    );
};

export default SidePanel;