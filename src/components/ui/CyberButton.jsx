export default function CyberButton({ children, variant = 'cyan' }) {
    const variants = {
      cyan: {
        stroke: '#00f0ff',
        fill: 'transparent',
        glow: '#00f0ff80',
      },
      'cyan-fill': {
        stroke: '#00f0ff',
        fill: '#00f0ff',
        glow: '#00f0ff',
      },
      black: {
        stroke: '#000000',
        fill: 'transparent',
        glow: '#000000',
      },
    };
  
    const { stroke, fill, glow } = variants[variant] || variants['cyan'];
  
    return (
      <button className="relative group w-[232px] h-[48px] overflow-visible">
        <svg
          width="232"
          height="48"
          viewBox="0 0 232 48"
          xmlns="http://www.w3.org/2000/svg"
          className="absolute top-0 left-0 pointer-events-none"
        >
          <defs>
            <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
              <feDropShadow dx="0" dy="0" stdDeviation="3" floodColor={glow} />
            </filter>
          </defs>
          <path
            d="
              M20 0
              H232
              V29.8571
              L215.35 48
              H0
              V25
              H25
              V20
              H5
              V10
              L5 0
              Z"
            fill={fill}
            stroke={stroke}
            strokeWidth="2"
            filter="url(#glow)"
          />
        </svg>
        <span className="relative z-10 font-bold text-black group-hover:text-white transition-colors duration-200">
          {children}
        </span>
      </button>
    );
  }