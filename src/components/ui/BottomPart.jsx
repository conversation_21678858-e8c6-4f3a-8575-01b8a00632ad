import React from 'react';
import TextType  from './TypingText';

// This component remains unchanged from your original code.
export default function BottomPart() {
  return (
    <div className="absolute bottom-0 right-0 z-50 w-screen overflow-x-hidden">
      <img src="/custom_svg/cyan_bottom.svg" alt="" className="w-full scale-130" />
    </div>
  );
}

export const LegendSection = () => {
  /**
   * Generates a square grid of dots.
   * @param {number} gridSize - The number of dots along one axis of the grid (e.g., 20 for a 20x20 grid).
   * @returns {Array<JSX.Element>} - An array of div elements representing the dots.
   */
  const generateDots = (gridSize) => {
    const dots = [];
    for (let i = 0; i < gridSize; i++) {
      for (let j = 0; j < gridSize; j++) {
        dots.push(
          <div
            key={`dot-${i}-${j}`}
            className="absolute w-0.5 h-0.5 bg-cyan-700 opacity-60 rounded-full"
            style={{
              // Position dots in the center of their "grid cell"
              top: `${(i + 0.5) * (100 / gridSize)}%`,
              left: `${(j + 0.5) * (100 / gridSize)}%`,
              transform: 'translate(-50%, -50%)',
            }}
          ></div>
        );
      }
    }
    return dots;
  };

  return (
    <div className="fixed bottom-0 z-40 w-full h-[20vh] flex justify-center items-center ">
      <div className="relative h-[80%] w-[60%] max-w-4xl">
        {/* Corner Brackets */}
        <div className="absolute top-0 left-0 w-8 h-8 border-l-2 border-t-2 border-cyan-400"></div>
        <div className="absolute top-0 right-0 w-44 h-8 border-r-2 border-t-2 border-cyan-400"></div>
        <div className="absolute bottom-0 left-0 w-8 h-8 border-l-2 border-b-2 border-cyan-400"></div>
        <div className="absolute bottom-0 right-0 w-8 h-8 border-r-2 border-b-2 border-cyan-400"></div>

        <div className="relative z-10 w-full h-full flex flex-col items-center justify-center text-center text-4xl md:text-7xl px-4 ">

          <TextType 
          text={["Welcome to IRIS","Welcome to IRIS"]}
          typingSpeed={50}
          pauseDuration={3500}
          showCursor={true}
          cursorCharacter="_" 
          />

        </div>
      </div>

      {/* Dot Grid Background */}
      <div className="absolute inset-0 pointer-events-none">
        {/* Generating a 20x20 square grid for a denser effect */}
        {generateDots(20)}
      </div>
    </div>
  );
};
