import React, { useState, useEffect } from 'react';
import { useStore } from '../../state/UseStore'; 
import { use } from 'framer-motion/m';


const RandomNumbers = ({ className, length = 4 }) => {
  const [numbers, setNumbers] = useState('');
  useEffect(() => {
    const generateNumbers = () => {
      let randomNumbers = '';
      for (let i = 0; i < length; i++) {
        randomNumbers += Math.floor(Math.random() * 10);
      }
      setNumbers(randomNumbers.slice(0, 2) + ' ' + randomNumbers.slice(2));
    };
    generateNumbers();
    const interval = setInterval(generateNumbers, 1500 + Math.random() * 1000);
    return () => clearInterval(interval);
  }, [length]);
  return <div className={`text-xs text-white/50 ${className}`}>{numbers}</div>;
};


// The main loader component
const FuturisticLoader = () => { 
  const progress = useStore((state) => state.progress);

  const counter = Math.round(progress);
  const totalBlocks = 7;
  const loadedBlocks = Math.min(Math.floor((progress / 100) * totalBlocks), totalBlocks);

  

  return (
    <div className="bg-black text-white flex items-center justify-center w-full h-full p-4 z-[1000]">
      <div className="relative w-full max-w-4xl flex flex-col items-center ">
        <div className="absolute -top-12">
          <div className="glitch" data-text="LOADING SYSTEM">LOADING SYSTEM</div>
        </div>
        <div className="w-full flex items-center justify-around">
          <div className="relative flex items-center w-10 h-10">
            <div className="absolute left-0 w-8 h-8 border border-white/80 flex items-center justify-center text-2xl font-thin text-white/80">+</div>
          </div>
          <div className="flex-grow flex items-center justify-center gap-2 mx-4">
            {Array(totalBlocks).fill(0).map((_, i) => (
              <div key={i} className={`w-full h-8 border-2 transition-colors duration-300 ${i < loadedBlocks ? 'bg-cyan-400/80 border-cyan-300 box-glow' : 'border-cyan-900/80'}`}></div>
            ))}
          </div>
          <div className="flex items-center justify-center space-x-4 text-3xl text-cyan-400 text-glow font-bold">
            <span className="text-white/80">&lt;</span>
            <span>{counter}</span>
            <span className="text-white/80">&gt;</span>
          </div>
          <div className="relative flex items-center w-14 h-10">
            <div className="absolute right-0 flex items-center space-x-3">
              <span className="text-sm text-white/80">x2</span>
              <span className="text-sm text-white/80">x3</span>
            </div>
          </div>
        </div>
        <div className="relative w-full max-w-xl mt-4 flex justify-around items-center">
          <RandomNumbers />
          <div className="flex items-center space-x-2">
            <span className="text-sm tracking-[0.2em] text-white/50">INITIALIZING...</span>
          </div>
          <RandomNumbers />
        </div>
      </div>
    </div>
  );
};

export default FuturisticLoader;