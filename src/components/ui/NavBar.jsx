import { useState, useEffect } from 'react';
import Button from './Button';
import { motion, AnimatePresence } from 'framer-motion';
import { useStore } from '../../state/UseStore';

const NAV_TABS = [
    { label: 'Home', value: 'Home' },
    { label: 'About', value: 'About' },
    { label: 'Services', value: 'Services' },
    { label: 'Contact us', value: 'Contact-us' },
];

const VISIBILITY_THRESHOLD_PX = 980;

export default function NavBar() {

    const [isNavVisible, setIsNavVisible] = useState(false);

    useEffect(() => {

        const checkDeviceWidth = () => {
            console.log(window.innerWidth)
          if (window.innerWidth < VISIBILITY_THRESHOLD_PX) {
            setIsNavVisible(false);
          } else {
            setIsNavVisible(true)
          }
        };
    
        checkDeviceWidth();

        window.addEventListener('resize', checkDeviceWidth);

        return () => {
          window.removeEventListener('resize', checkDeviceWidth);
        };
      }, []); 

    
    const selectedSectionName = useStore(state => state.selectedSectionName);
   

    const [activeTab, setActiveTab] = useState('Home');

    return (
        <>
       { isNavVisible && <AnimatePresence>
                <motion.div
                    style={{display: (selectedSectionName !== null  ? 'none' : 'block')}}
                    initial={{ y: '-100%' }}
                    animate={{ y: 0 }}
                    exit={{ y: '-100%' }} 
                    transition={{ duration: 1, ease: 'easeInOut' }}
                    className="nav-container z-[1000000]! flex justify-center"
                >
                    <div className="nav-border-layer"></div>
                    <div className="nav-content-layer flex justify-center">
                        {NAV_TABS.map(tab => (
                            <Button
                                key={tab.value}
                                label={tab.label}
                                active={activeTab === tab.value}
                                onClick={() => setActiveTab(tab.value)}
                            />
                        ))}
                    </div>
                </motion.div>
        </AnimatePresence> }
        </>
    );
}



