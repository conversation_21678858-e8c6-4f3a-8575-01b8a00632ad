import React, { useState, useEffect } from 'react';




export default function RotateDevice() {
  // State to track if the prompt should be visible
  const [isPromptVisible, setIsPromptVisible] = useState(false);

  // The width threshold to show the prompt
  const VISIBILITY_THRESHOLD_PX = 500;

  useEffect(() => {
    // Function to check the window width and update state
    const checkDeviceWidth = () => {
      if (window.innerWidth < VISIBILITY_THRESHOLD_PX) {
        setIsPromptVisible(true);
      } else {
        setIsPromptVisible(false);
      }
    };

    // Check the width when the component mounts
    checkDeviceWidth();

    // Add a resize event listener to dynamically check width
    window.addEventListener('resize', checkDeviceWidth);

    // Cleanup function to remove the event listener when the component unmounts
    return () => {
      window.removeEventListener('resize', checkDeviceWidth);
    };
  }, []); 

  if (!isPromptVisible) {
    return null;
  }



  return (
    <>
      <div className="fixed inset-0 h-screen w-screen flex flex-col items-center justify-center bg-black/90 backdrop-blur-sm z-50 p-4 md:hidden z-[10000000000]!">
        {/* <div className="text-white mb-8 animate-pulse">
            <Smartphone size={64} strokeWidth={1} />
        </div> */}
        {/* The element with the glitch effect */}
        <div className="glitch" data-glitch="PLEASE ROTATE YOUR DEVICE">
          PLEASE ROTATE YOUR DEVICE
        </div>
        <p className="text-white/70 mt-6 text-center text-lg">For the best experience, please rotate your device to landscape mode.</p>
      </div>
    </>
  );
}
