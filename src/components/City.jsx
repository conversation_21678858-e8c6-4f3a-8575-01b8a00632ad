import { <PERSON>vas, useThree, use<PERSON>rame } from '@react-three/fiber';
import { OrbitControls, Environment, useGLTF, Html, Line, useProgress } from '@react-three/drei';
import React, { Suspense, useEffect, useMemo, useRef, useState } from 'react';
import * as THREE from 'three';
import { useControls } from 'leva';
import Effects from './Effects';
import { useStore } from '.././state/UseStore';


useGLTF.preload('/WEBSITE_CITY_modifying_CLEANED.glb');

/**
 * Calculates the world center of a given 3D object's bounding box.
 * @param {THREE.Object3D} object - The object to find the center of.
 * @returns {THREE.Vector3} The center point of the object.
 */
function getModelCenter(object) {
    const box = new THREE.Box3().setFromObject(object);
    const center = new THREE.Vector3();
    box.getCenter(center);
    return center;
}


function ProgressTracker() {
    const { progress, active } = useProgress();
    const { setProgress, setLoaded } = useStore(state => state.actions);

    useEffect(() => {
        setProgress(progress);
        // When loading is complete, update the global state
        if (!active && progress === 100) {
            setTimeout(() => setLoaded(true), 5);
        }
    }, [progress, active, setProgress, setLoaded]);

    return null;
}


function CyberButton({ children, variant = 'cyan', width = 140, height = 40, ...props }) {
    const variants = {
        cyan: {
            stroke: '#00f0ff',
            fill: 'rgba(0, 40, 40, 0.7)',
            glow: 'drop-shadow(0 0 3px rgba(0, 240, 255, 0.1))',
            textColor: 'white',
        },
        'cyan-fill': {
            stroke: '#00f0ff',
            fill: 'rgba(0, 200, 200, 0.9)',
            glow: 'drop-shadow(0 0 5px rgba(0, 240, 255, 0.5))',
            textColor: 'white',
        },
    };

    const { stroke, fill, glow, textColor } = variants[variant] || variants.cyan;

    const backgroundUri = useMemo(() => {
        const path = `M10 0 H${width} V${height - 10} L${width - 10} ${height} H0 V10 L10 0 Z`;
        const svgString =
            `<svg width="${width}" height="${height}" viewBox="0 0 ${width} ${height}" 
      xmlns="http://www.w3.org/2000/svg">
      <path d="${path}" fill="${fill}" stroke="${stroke}" stroke-width="2"/>
      </svg>`;
        return `url("data:image/svg+xml;base64,${btoa(svgString)}")`;
    }, [fill, stroke, width, height]);

    return (
        <div
            {...props}
            className="group cursor-pointer flex items-center justify-center hover:text-cyan-400! hover:scale(1.05) transition-colors duration-200"
            style={{
                width: `${width}px`,
                height: `${height}px`,
                backgroundImage: backgroundUri,
                backgroundSize: 'contain',
                backgroundRepeat: 'no-repeat',
                filter: glow,
            }}
        >
            <span className="font-bold transition-colors duration-200 text-md " style={{ color: textColor }}>
                {children}
            </span>
        </div>
    );
}

/**
 * Renders a label and a line pointing to a specific section of the city.
 * Fades in and out based on whether any section is selected.
 */
function SectionLabel({ section, isHovered, isSelected, onButtonHover, onButtonLeave, onButtonClick, isAnySectionSelected }) {
    const { linePoints, labelPosition } = useMemo(() => {
        const center = getModelCenter(section);
        const start = center.clone();
        const elbow = start.clone().setY(start.y + 6);
        const end = elbow.clone().setX(elbow.x + 10).setY(elbow.y + 6);
        return { linePoints: [start, elbow, end], labelPosition: end };
    }, [section]);

    const lineRef = useRef();

    useFrame(() => {
        if (lineRef.current) {
            const targetOpacity = isAnySectionSelected ? 0 : 1;
            lineRef.current.material.opacity = THREE.MathUtils.lerp(lineRef.current.material.opacity, targetOpacity, 0.1);
        }
    });

    return (
        <group>
            <Line
                ref={lineRef}
                points={linePoints}
                color={isSelected ? '#00f0ff' : 'white'}
                lineWidth={1}
                dashed={false}
                transparent={true} 
            />
            <Html
                position={labelPosition}
                style={{
                    transform: 'translateY(-100%)',
                    transition: 'opacity 0.3s ease-in-out',
                    opacity: isAnySectionSelected ? 0 : 1,
                    pointerEvents: isAnySectionSelected ? 'none' : 'auto',
                }}
            >
                <CyberButton
                    variant={isHovered || isSelected ? 'cyan-fill' : 'cyan'}
                    onPointerEnter={onButtonHover}
                    onPointerLeave={onButtonLeave}
                    onClick={(e) => {
                        e.stopPropagation();
                        onButtonClick(section.name, getModelCenter(section));
                    }}
                >
                    {section.name}
                </CyberButton>
            </Html>
        </group>
    );
}


function InteractiveCity({ onCenterComputed, onSectionCentersComputed, metalness, roughness, selectedSectionName, onSectionSelect }) {
    const { scene } = useGLTF('/WEBSITE_CITY_modifying_CLEANED.glb');
    const [hoveredName, setHoveredName] = useState(null);

    const interactiveNames = useMemo(() => ['Section1', 'Section2', 'Section3', 'Section4', 'Section5', 'Section6'], []);
    const staticNames = useMemo(() => ['JoinSection', 'Circle'], []);

    useEffect(() => {
        if (scene) onCenterComputed?.(getModelCenter(scene));
    }, [scene, onCenterComputed]);

    const allSections = useMemo(() => {
        if (!scene) return [];
        const processNode = (name) => {
            const originalNode = scene.getObjectByName(name);
            if (!originalNode) return null;
            const clonedNode = originalNode.clone(true);
            clonedNode.name = name;
            clonedNode.traverse((child) => {
                if (child.isMesh) {
                    child.material = child.material.clone();
                    child.material.transparent = true;
                    if (interactiveNames.includes(name)) {
                        child.material.emissive = new THREE.Color('#000000');
                    }
                    if (child.name !== 'Circle') {
                        child.material.metalness = metalness;
                        child.material.roughness = roughness;
                    } else {
                        child.material.metalness = 0.5;
                        child.material.roughness = 0.5;
                        child.material.color.set('black');
                    }
                }
            });
            return clonedNode;
        };
        return [...interactiveNames, ...staticNames].map(processNode).filter(Boolean);
    }, [scene, metalness, roughness, interactiveNames, staticNames]);

    const interactiveSections = useMemo(() => allSections.filter(s => interactiveNames.includes(s.name)), [allSections, interactiveNames]);

    useEffect(() => {
        const centers = {};
        interactiveSections.forEach(section => {
            centers[section.name] = getModelCenter(section);
        });
        onSectionCentersComputed(centers);
    }, [interactiveSections, onSectionCentersComputed]);

    useFrame(() => {
        allSections.forEach((section) => {
            const isInteractive = interactiveNames.includes(section.name);
            const isSelected = section.name === selectedSectionName;
            const isHovered = section.name === hoveredName;
            const isBaseCircle = section.name === 'Circle';

            let targetOpacity = 1.0;
            if (selectedSectionName) {
                targetOpacity = (isSelected || isBaseCircle) ? 1.0 : 0.0;
            } else if (hoveredName) {
                targetOpacity = (isHovered || isBaseCircle) ? 1.0 : 0.2;
            }

            const isHighlighted = isHovered && !isSelected;
            const targetEmissiveColor = new THREE.Color(isHighlighted ? '#00FFFF' : '#000000');

            section.traverse((child) => {
                if (child.isMesh && child.material) {
                    child.material.opacity = THREE.MathUtils.lerp(child.material.opacity, targetOpacity, 0.1);
                    if (isInteractive) {
                        child.material.emissive.lerp(targetEmissiveColor, 0.1);
                    }
                }
            });
        });
    });

    return (
        <group>
            {allSections.map((node) => (
                <primitive
                    key={node.uuid}
                    object={node}
                    onClick={interactiveNames.includes(node.name) ? (e) => { e.stopPropagation(); onSectionSelect(node.name); } : undefined}
                />
            ))}
            {interactiveSections.map((section) => (
                <SectionLabel
                    key={section.uuid}
                    section={section}
                    isHovered={section.name === hoveredName}
                    isSelected={section.name === selectedSectionName}
                    isAnySectionSelected={!!selectedSectionName} // Pass the global selection state
                    onButtonHover={() => setHoveredName(section.name)}
                    onButtonLeave={() => setHoveredName(null)}
                    onButtonClick={onSectionSelect}
                />
            ))}
        </group>
    );
}

function CameraRig({ target, position, isSelected }) {
    const { camera, controls } = useThree();

    useFrame((state, delta) => {
        if (controls) {
            const speed = delta * 2;
            controls.target.lerp(target, speed);
            if (isSelected) {
                camera.position.lerp(position, speed*5);
            }
            controls.update();
        }
    });

    return null;
}


function City() {
    const [modelCenter, setModelCenter] = useState(new THREE.Vector3(0, 0, 0));
    const [sectionCenters, setSectionCenters] = useState({});
    const [selectedSectionName, setSelectedSectionName] = useState(null);

    // FIX: Move useStore hooks to top level
    const selectedSubSection = useStore(state => state.selectedSubSectionName);
    const setSelectedSectionNameStore = useStore(state => state.setSelectedSectionName);

    const { envIntensity, dirLight, metalness, roughness } = useControls({
        envIntensity: { value: 2.8, min: 0, max: 3, step: 0.01, label: 'Environment' },
        dirLight: { value: 0.5, min: 0, max: 5, step: 0.01, label: 'Light' },
        metalness: { value: 1, min: 0, max: 1, step: 0.01, label: 'Metalness' },
        roughness: { value: 0.4, min: 0, max: 1, step: 0.01, label: 'Roughness' },
    });

    const orbitControls = useControls('Orbit Controls', {
        autoRotate: true,
        autoRotateSpeed: { value: 0.01, min: -2, max: 2, step: 0.1 },
        enablePan: true,
        enableZoom: true,
        enableRotate: true,
        minDistance: 80,
        maxDistance: 200,
        minPolarAngle: -5,
        maxPolarAngle: 1.40,
        dampingFactor: { value: 0.1, min: 0, max: 1 },
        rotateSpeed: { value: 0.5, min: 0, max: 5 },
    });


    const handleSectionSelect = (name) => {
        if (selectedSubSection !== null) {
            useStore.setState({ selectedSubSectionName: null });
        } else {
            const newSelection = selectedSectionName === name ? null : name;
            setSelectedSectionName(newSelection);
            setSelectedSectionNameStore(newSelection);
            useStore.setState({ selectedSectionName: newSelection });
        }
    };

    const { cameraTarget, cameraPosition } = useMemo(() => {
        if (selectedSectionName && sectionCenters[selectedSectionName]) {
            const target = sectionCenters[selectedSectionName];
            const position = target.clone().add(new THREE.Vector3(15, 15, 15));
            return { cameraTarget: target, cameraPosition: position };
        }
        const defaultPosition = new THREE.Vector3(50, 10, 30);
        return { cameraTarget: modelCenter, cameraPosition: defaultPosition };
    }, [selectedSectionName, modelCenter, sectionCenters]);

    return (
        <>
            <ProgressTracker />

            
            <div style={{
                position: 'absolute',
                top: '40px',
                left: '40px',
                zIndex: 100, 
                opacity: selectedSectionName ? 1 : 0,
                pointerEvents: selectedSectionName ? 'auto' : 'none',
                transition: 'opacity 0.5s 0.3s ease-in-out',
            }}>
                <CyberButton 
                    onClick={() => handleSectionSelect(null)}
                    width={60} 
                    height={40}
                >
                    {'<'}
                </CyberButton>
            </div>

            <Canvas
                camera={{ position: [50, 10, 30], fov: 50 }}
                shadows
                dpr={[1, 2]}
                className="w-scree! h-screen!"
            >
                <Suspense fallback={null}>
                    {/* <Effects /> */}
                    
                    <directionalLight position={[5, 10, 5]} intensity={dirLight} castShadow />
                    <Environment files="/dawn.exr" background environmentIntensity={envIntensity} />

                    
                    <OrbitControls makeDefault enableDamping {...orbitControls} />
                    <CameraRig target={cameraTarget} position={cameraPosition} isSelected={!!selectedSectionName} />

                    
                    <InteractiveCity
                        onCenterComputed={setModelCenter}
                        onSectionCentersComputed={setSectionCenters}
                        metalness={metalness}
                        roughness={roughness}
                        selectedSectionName={selectedSectionName}
                        onSectionSelect={handleSectionSelect}
                    />
                </Suspense>
            </Canvas>
        </>
    );
}

export default City;
