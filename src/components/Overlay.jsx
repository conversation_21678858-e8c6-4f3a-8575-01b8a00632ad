import CyberButton from './ui/CyberButton'
import CyberNavBar from './ui/NavBar'
import Logo from './ui/Logo'
import BottomPart from './ui/BottomPart'
import {LegendSection} from './ui/BottomPart'
import SidePanel from './ui/SIdePanel'
import React, { useEffect, useState } from 'react';
import { useStore } from '../state/UseStore';

export default function Overlay() {
    const [showLegend, setShowLegend] = useState(false);
    const setSelectedSectionName = useStore(state => state.setSelectedSectionName);

    useEffect(() => {
        const timer = setTimeout(() => setShowLegend(false), 10000);
        return () => clearTimeout(timer);
    }, []);

    return (
        <>
        <Logo />
        <div className="absolute top-0 z-50! bg-transparent min-w-[100vw] h-[100px] flex justify-center items-center">
           <div className='mt-5'>
            <CyberNavBar />
           </div>

           <SidePanel />
        </div>

        <BottomPart />
        {showLegend && <LegendSection />}
        </>
    )
}