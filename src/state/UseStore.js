import { create } from 'zustand';

export const useStore = create((set) => ({
    selectedSectionName: null,
    sectionCenters: {},
    setSelectedSectionName: (name) => set(state => ({ 
        selectedSectionName: state.selectedSectionName === name ? null : name 
    })),
    setSectionCenters: (centers) => set({ sectionCenters: centers }),
    selectedSubSectionName: null,
    setSelectedSubSectionName: (name) => set({ selectedSubSectionName: name }),


    progress: 0,
    isLoaded: false,
    actions: {
        setProgress: (progress) => set({ progress }),
        setLoaded: (isLoaded) => set({ isLoaded }),
    },
}));

