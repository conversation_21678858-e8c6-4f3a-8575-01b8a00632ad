import React from 'react';

const GlitchText = ({ text, className = '' }) => {
  return (
    // The main container. 'relative' is crucial for positioning the glitch layers.
    <h2
      className={`
        relative inline-block text-5xl font-bold text-white 
        lg:text-8xl tracking-[10px] uppercase
        drop-shadow-[0_1px_3px_rgba(0,0,0,0.8)]
        ${className}
      `}
    >
      {/* Layer 1: The main, visible text. It also gets a clip-path animation. */}
      <span className="relative z-10 animate-paths">{text}</span>

      {/* Layer 2: The first glitch layer (Pink). aria-hidden for accessibility. */}
      <span
        aria-hidden="true"
        className={`
          absolute top-0 left-0 z-0 w-full h-full 
          text-glitch-pink 
          animate-paths animate-opacity animate-font-alt animate-movement-alt
        `}
      >
        {text}
      </span>

      {/* Layer 3: The second glitch layer (Cyan). */}
      <span
        aria-hidden="true"
        className={`
          absolute top-0 left-0 z-0 w-full h-full 
          text-glitch-cyan 
          animate-paths animate-opacity animate-font animate-movement
        `}
      >
        {text}
      </span>
    </h2>
  );
};

export default GlitchText;